﻿# Technology Stack

## Core Technologies
- **Python 3.8+**: Main programming language
- **UV**: Modern Python package manager and dependency resolver
- **Pydantic**: Data validation and settings management
- **TQDM**: Progress bars for long-running operations
- **orjson**: Fast JSON parsing and serialization

## Development Tools
- **Black**: Code formatting
- **Flake8**: Code linting
- **Pytest**: Testing framework

## Project Structure
- **pyproject.toml**: Project configuration and dependencies
- **uv.lock**: Locked dependency versions
- **src/**: Source code directory
- **run.bat**: Application launcher
- **uv_init.bat**: Environment initialization script

## Key Features
- Long path support for Windows (bypasses 260-character limit)
- Robust error handling for malformed data
- Interactive CLI with progress reporting
- Modular architecture for easy maintenance
