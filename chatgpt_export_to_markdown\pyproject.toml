[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "chatgpt-export-to-markdown"
version = "1.0.0"
description = "Robust ChatGPT export converter to Markdown format"
requires-python = ">=3.8"

dependencies = [
    "pydantic>=2.4.2",
    "tqdm>=4.66.1",
]

[project.optional-dependencies]
dev = ["pytest>=7.0.0", "black>=22.0.0", "flake8>=4.0.0"]

[project.scripts]
chatgpt-export-to-markdown = "src.main:main"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.black]
line-length = 88
target-version = ['py38']
