﻿# ChatGPT Export to Markdown

A robust utility for converting ChatGPT conversation exports to Markdown format.

## Features

- Converts ChatGPT export zip files to Markdown format
- Handles long file paths (bypasses Windows 260-character limit)
- Robust error handling for corrupted or invalid conversations
- Detailed logging and progress reporting

## Installation

This project uses uv for dependency management.

1. Install [uv](https://docs.astral.sh/uv/getting-started/installation/) if you haven't already
2. Clone this repository
3. Run the initialization script:

`ash
uv_init.bat
`

## Usage

Run the application with:

`ash
run.bat
`

Or with command-line arguments:

`ash
run.bat --zip-file "path/to/export.zip" --output-dir "path/to/output" --verbose
`

### Command-line Arguments

- --zip-file: Path to the ChatGPT export zip file
- --output-dir: Directory to save the markdown files (default: ~/Documents/ChatGPT Data/Markdown)
- --verbose: Show detailed error information

## Requirements

- Python 3.8 or higher
- Dependencies (automatically installed by uv):
  - pydantic
  - tqdm
  - orjson
